import config from "@app/lib/api/config";
import type Logger from "@app/logger/logger";
import { makeScript } from "@app/scripts/helpers";
import type { CoreAppAPIRelocationBlob } from "@app/temporal/relocation/activities/types";
import type { CoreAPIDataset } from "@app/types";
import { CoreAPI } from "@app/types";

export async function getApp({
  dustAPIProjectId,
  logger,
}: {
  dustAPIProjectId: string;
  logger: typeof Logger;
}) {
  const coreAPI = new CoreAPI(config.getCoreAPIConfig(), logger);
  const coreSpec = await coreAPI.getSpecificationHashes({
    projectId: dustAPIProjectId,
  });
  if (coreSpec.isErr()) {
    throw new Error("Failed to get core specification hashes");
  }
  logger.info({ coreSpec }, "Core spec");

  const specsToFetch = coreSpec.value.hashes;

  const coreSpecifications: Record<string, string> = {};

  if (specsToFetch) {
    for (const hash of specsToFetch) {
      const coreSpecification = await coreAPI.getSpecification({
        projectId: dustAPIProjectId,
        specificationHash: hash,
      });

      if (coreSpecification.isErr()) {
        throw new Error("Failed to get core specification");
      }
      coreSpecifications[hash] = coreSpecification.value.specification.data;
    }
  }
  logger.info({ specsToFetch }, "specs to fetch");

  const dataSetsToFetch = await coreAPI.getDatasets({
    projectId: dustAPIProjectId,
  });

  if (dataSetsToFetch.isErr()) {
    throw new Error("Failed to get datasets");
  }

  logger.info({ dataSetsToFetch }, "datasets to fetch");

  const datasets: CoreAPIDataset[] = [];
  for (const datasetId of Object.keys(dataSetsToFetch.value.datasets)) {
    const dataSetVersions = dataSetsToFetch.value.datasets[datasetId];
    for (const dataSetVersion of dataSetVersions) {
      const apiDataset = await coreAPI.getDataset({
        projectId: dustAPIProjectId,
        datasetName: datasetId,
        datasetHash: dataSetVersion.hash,
      });

      if (apiDataset.isErr()) {
        logger.error(
          {
            projectId: dustAPIProjectId,
            datasetName: datasetId,
            datasetHash: dataSetVersion.hash,
            error: apiDataset.error,
          },
          "Failed to get datasets"
        );
        throw new Error(
          `Failed to get dataset ${datasetId}: ${apiDataset.error.message}`
        );
      }

      datasets.push(apiDataset.value.dataset);
    }
  }

  const blobs: CoreAppAPIRelocationBlob = {
    blobs: {
      apps: [
        {
          coreSpecifications,
          datasets,
        },
      ],
    },
  };

  return { blobs };
}

makeScript({}, async (_, logger) => {
  const { blobs } = await getApp({ dustAPIProjectId: "11704", logger });
  logger.info({ blobs }, "Blobs");
});
